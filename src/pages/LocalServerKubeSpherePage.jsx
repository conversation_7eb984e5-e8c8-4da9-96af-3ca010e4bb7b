import React, {useState, useEffect} from 'react'
import ArchitectureDiagram from '../components/ArchitectureDiagram'
import EnvironmentGuide from '../components/EnvironmentGuide'
import FAQ from '../components/FAQ'
import {
    ServerIcon,
    CogIcon,
    QuestionMarkCircleIcon,
    CheckCircleIcon,
    ClockIcon,
    UserGroupIcon,
    CpuChipIcon,
    CircleStackIcon,
    ShieldCheckIcon,
    ChartBarIcon,
    BoltIcon,
    GlobeAltIcon,
    ArrowRightIcon,
    PlayIcon,
    DocumentTextIcon,
    SparklesIcon,
    CubeIcon
} from '@heroicons/react/24/outline'
import {
    ServerIcon as ServerSolid, ShieldCheckIcon as ShieldSolid, CpuChipIcon as CpuSolid, CircleStackIcon as StorageSolid
} from '@heroicons/react/24/solid'

const LocalServerKubeSpherePage = () => {
    const [activeTab, setActiveTab] = useState('overview')

    const handleTabChange = (tabId) => {
        setActiveTab(tabId)
        // 滚动到标签内容的开始位置，考虑导航栏高度
        setTimeout(() => {
            const tabContentElement = document.querySelector('.tab-content')
            const tabNavElement = document.querySelector('.tab-navigation')
            if (tabContentElement && tabNavElement) {
                const navHeight = tabNavElement.offsetHeight
                const elementTop = tabContentElement.offsetTop
                const offsetPosition = elementTop - navHeight - 70 // 额外20px间距

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                })
            }
        }, 100) // 短暂延迟确保内容已渲染
    }

    const tabs = [{id: 'overview', name: '概览', icon: SparklesIcon, description: '集群整体概况'}, {
        id: 'architecture',
        name: '架构图',
        icon: CubeIcon,
        description: '可视化集群架构'
    }, {id: 'environment', name: '环境配置', icon: CogIcon, description: '部署与配置指南'}, {
        id: 'faq',
        name: '问题解答',
        icon: QuestionMarkCircleIcon,
        description: '常见问题'
    },]

    const stats = [{
        label: '虚拟机节点',
        value: '20+',
        unit: '个',
        icon: ServerIcon,
        gradient: 'from-emerald-500 to-emerald-600',
        description: 'Kubernetes工作节点'
    }, {
        label: '系统可用性',
        value: '99.9%',
        unit: '',
        icon: ShieldSolid,
        gradient: 'from-purple-500 to-purple-600',
        description: '高可用保障'
    }, {
        label: '集群稳定性',
        value: '365',
        unit: '天',
        icon: UserGroupIcon,
        gradient: 'from-orange-500 to-orange-600',
        description: '全年稳定运行'
    },]

    const features = [{
        icon: ShieldCheckIcon,
        title: '高可用架构',
        description: '三主节点集群确保控制平面高可用，etcd分布式存储保障数据安全',
        highlights: ['3个Master节点', 'etcd集群', 'API Server负载均衡'],
        color: 'blue'
    }, {
        icon: CpuChipIcon,
        title: '智能负载均衡',
        description: 'HAProxy + Keepalived实现API服务器负载均衡，VIP自动漂移',
        highlights: ['VIP自动漂移', '健康检查', '流量智能分发'],
        color: 'emerald'
    }, {
        icon: CircleStackIcon,
        title: '分布式存储',
        description: 'NFS网络存储双节点互备，支持动态存储供应和数据冗余',
        highlights: ['双节点互备', '动态供应', '数据冗余'],
        color: 'purple'
    }, {
        icon: ChartBarIcon,
        title: '全栈监控',
        description: 'KubeSphere内置Prometheus监控体系，实时监控集群状态',
        highlights: ['实时监控', '日志聚合', '智能告警'],
        color: 'orange'
    }]

    const techSpecs = [
        {
            label: 'Kubernetes',
            value: '管理容器调度',
            icon: CubeIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-blue-600 rounded-xl mb-4"
        },
        {
            label: 'KubeSphere',
            value: 'Kubernetes管理平台',
            icon: SparklesIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-sky-600 rounded-xl mb-4"
        },
        {
            label: 'Docker',
            value: '容器运行时',
            icon: ServerIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-emerald-600 rounded-xl mb-4"
        },
        {
            label: 'Calico',
            value: '网络插件',
            icon: GlobeAltIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-red-600 rounded-xl mb-4"
        },
        {
            label: 'HAProxy',
            value: '负载均衡',
            icon: BoltIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-violet-600 rounded-xl mb-4"
        },
        {
            label: 'NFS',
            value: '存储方案',
            icon: CircleStackIcon,
            className: "inline-flex items-center justify-center w-12 h-12 bg-teal-700 rounded-xl mb-4"
        }]

    return (<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
        {/* Enhanced Tab Navigation */}
        <div className="tab-navigation  bg-white border-b border-gray-200 shadow-sm">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <nav className="flex space-x-1 py-4">
                    {tabs.map((tab) => {
                        const Icon = tab.icon
                        const isActive = activeTab === tab.id
                        return (<button
                            key={tab.id}
                            onClick={() => handleTabChange(tab.id)}
                            className={`relative flex items-center px-6 py-4 rounded-xl font-medium transition-all duration-200 ${isActive ? 'bg-blue-600 text-white shadow-lg' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'}`}
                        >
                            <Icon className={`mr-3 h-5 w-5 ${isActive ? 'text-white' : 'text-gray-500'}`}/>
                            <div className="text-left">
                                <div className="font-semibold">{tab.name}</div>
                                <div
                                    className={`text-xs mt-0.5 ${isActive ? 'text-blue-100' : 'text-gray-500'}`}>
                                    {tab.description}
                                </div>
                            </div>
                        </button>)
                    })}
                </nav>
            </div>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
            {/* Overview Tab */}
            {activeTab === 'overview' && (<div className="">
                {/* Hero Section */}
                <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 mb-16">
                    <div className="px-4 sm:px-6 lg:px-8 py-24">
                        <div className="text-center">
                            <div className="space-y-8">
                                <div>
                                    <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 tracking-tight">
                                        <span className="block">KubeSphere</span>
                                        <span
                                            className="block bg-gradient-to-r from-blue-200 via-white to-indigo-200 bg-clip-text text-transparent">
                                        高可用集群
                                    </span>
                                    </h1>
                                    <p className="text-xl md:text-2xl text-blue-100/90 max-w-4xl mx-auto leading-relaxed font-light">
                                        本地机房Kubernetes集群解决方案
                                        <span className="block mt-2 text-lg text-blue-200/70">
                                        基于物理机虚拟化的高可用架构设计
                                    </span>
                                    </p>
                                </div>

                                {/* Status indicators */}
                                <div className="flex flex-wrap justify-center gap-6 pt-8">
                                    <div
                                        className="flex items-center space-x-3 px-6 py-3 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20">
                                        <div className="w-3 h-3 bg-green-400 rounded-full"/>
                                        <span className="text-white font-medium">生产环境运行</span>
                                    </div>
                                    <div
                                        className="flex items-center space-x-3 px-6 py-3 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20">
                                        <ShieldSolid className="w-5 h-5 text-blue-300"/>
                                        <span className="text-white font-medium">99.9% 可用性</span>
                                    </div>
                                    <div
                                        className="flex items-center space-x-3 px-6 py-3 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20">
                                        <CpuSolid className="w-5 h-5 text-purple-300"/>
                                        <span className="text-white font-medium">20+ 节点</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Features Section */}
                <section className="mb-16">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            核心特性
                        </h2>
                        <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
                            高可用架构设计，确保系统稳定性、可扩展性和安全性
                        </p>
                    </div>

                    <div className="max-w-7xl mx-auto  grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {features.map((feature, index) => {
                            const Icon = feature.icon
                            const colorClasses = {
                                blue: 'from-blue-500 to-blue-600',
                                emerald: 'from-emerald-500 to-emerald-600',
                                purple: 'from-purple-500 to-purple-600',
                                orange: 'from-orange-500 to-orange-600'
                            }

                            return (<div
                                key={index}
                                className="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg hover:shadow-xl"
                            >
                                <div className="flex items-start space-x-6">
                                    <div
                                        className={`flex-shrink-0 w-16 h-16 bg-gradient-to-br ${colorClasses[feature.color]} rounded-2xl flex items-center justify-center shadow-lg`}>
                                        <Icon className="h-8 w-8 text-white"/>
                                    </div>

                                    <div className="flex-1">
                                        <h3 className="text-2xl font-bold text-gray-900 mb-3">
                                            {feature.title}
                                        </h3>
                                        <p className="text-gray-700 leading-relaxed mb-6">
                                            {feature.description}
                                        </p>

                                        <div className="flex flex-wrap gap-2">
                                            {feature.highlights.map((highlight, idx) => (<span
                                                key={idx}
                                                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${colorClasses[feature.color]} text-white shadow-sm`}
                                            >
                                                                    <CheckCircleIcon className="w-4 h-4 mr-1"/>
                                                {highlight}
                                                                </span>))}
                                        </div>
                                    </div>
                                </div>
                            </div>)
                        })}
                    </div>
                </section>

                {/* Tech Specs */}
                <section className="mb-10">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            技术规格
                        </h2>
                        <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                            采用业界领先的开源技术栈，构建稳定可靠的容器化平台
                        </p>
                    </div>

                    <div className="max-w-7xl mx-auto  grid grid-cols-2 md:grid-cols-3 gap-6">
                        {techSpecs.map((spec, index) => {
                            const Icon = spec.icon
                            return (<div
                                key={index}
                                className="bg-white rounded-2xl p-6 border border-gray-200 shadow-lg"
                            >
                                <div className="text-center">
                                    <div
                                        className={spec.className}>
                                        <Icon className="h-6 w-6 text-white"/>
                                    </div>
                                    <h3 className="font-semibold text-gray-900 mb-2">{spec.label}</h3>
                                    <p className="text-gray-700 font-medium">{spec.value}</p>
                                </div>
                            </div>)
                        })}
                    </div>
                </section>
            </div>)}

            {/* Other tabs */}
            {activeTab === 'architecture' && <ArchitectureDiagram/>}
            {activeTab === 'environment' && <EnvironmentGuide/>}
            {activeTab === 'faq' && <FAQ/>}
        </div>
    </div>)
}

export default LocalServerKubeSpherePage
