import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import LocalClusterPage from './pages/LocalClusterPage'
import AliCloudPage from './pages/AliCloudPage'
import LocalServerKubeSpherePage from './pages/LocalServerKubeSpherePage'

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/local-cluster" element={<LocalClusterPage />} />
          <Route path="/local-cluster/kubesphere" element={<LocalServerKubeSpherePage />} />
          <Route path="/ali-cloud" element={<AliCloudPage />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
