做一个官网形式的网站，名称为：K8S集群部署架构，也算是一个文档吧，界面：
1、本地机房部署K8S集群
2、阿里云部署K8S集群
目前只要两个界面，后面我会补充更多

菜单需要支持二级菜单，鼠标悬浮显示二级
本地机房部署下：KubeSphere高可用部署集群

环境 node.js20 tailwindui react vite

先完成第一个，本地机房部署K8S集群，这是我的集群安装描述
我需要：
1、 需要有架构图形式的页面，每个节点点开可以查看该节点的详细信息，展示节点内的组件及描述
2、 介绍各个环境的使用方式
3、最后需要有常见问题解答

通过运营商申请到了一个固定公网IP为 *************，在云服务商中购买了域名为 test.com
域名解析：doc.test.com -> *************、api.test.com -> ************* 等等各种子域名都解析到这个IP地址
公网IP通过路由器交换机让三台物理机服务器可以连接网络使用
1. ************
2. ************
3. ************
4. ************
这些物理机使用VM平台装了若干虚拟机

路由器通过端口转发，将IP *************的80、443端口转发到了 ************这个IP的80、443端口
************由************物理机下的两台虚拟机使用Haproxy + Keepalived + Nginx 搭建的主从高可用方案出来的虚拟IP

************作为入口，主从Nginx都反向代理了相应的服务，服务使用K8S部署运维，部分服务部署在其他的虚拟机
例如：k8sweb管理端服务端口配置
upstream k8s-web {
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
}
server {
    server_name k8sweb.test.com;
    charset utf-8;
    location / {
        proxy_pass              http://k8s-web;
        proxy_cookie_path       /                   /;
    }
}
使用 k8sweb.test.com 访问k8s的web管理平台
我使用的这个k8s平台名为Kubesphere，可以配置集群网关与项目（命名空间）网关
项目网关信息：
访问方式:NodePort  网关地址:************ 节点端口: http:31166; https:31482
项目（命名空间）网关配置
upstream project-platform {
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
}
************主从nginx配置服务相关域名
server {
    server_name p.test.com c.test.com api.test.com;
    server_name openapi.test.com doc.open.test.com;
    # 包含其他的域名
    charset utf-8;
    location / {
        proxy_pass              http://project-platform;
        proxy_cookie_path       /                   /;
    }
}
之后在k8s中配置应用路由（Ingress），将对应域名与服务绑定，即可实现域名访问服务
应用路由配置(其中某一个举例)：
kind: Ingress
apiVersion: extensions/v1beta1
metadata:
  name: pos-web
  namespace: project-platform
  labels:
    app: pos-web
    app.kubernetes.io/instance: pos-web
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: pos-web
    app.kubernetes.io/version: 1.0.0
    helm.sh/chart: pos-web-1.0.0
    version: 1.0.0
  annotations:
    meta.helm.sh/release-name: pos-web
    meta.helm.sh/release-namespace: project-platform
spec:
  rules:
    - host: p.test.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              serviceName: pos-web
              servicePort: 80
这里开始补充k8s集群安装信息
************物理机器对应的虚拟机以及虚拟机在k8s集群中的角色如下
************（master01,etcd01）、************（node-01）、************（node-04）、************（node-07）
************（node-10）、************（node-ci-02）、************（nfs存储-01）

************物理机器对应的虚拟机以及虚拟机在k8s集群中的角色如下
************（master02,etcd02)、************（node-02）、************（node-05）
************（node-02)、************（node-11）、************（负载均衡-01）、************(nfs存储-02)

************物理机器对应的虚拟机以及虚拟机在k8s集群中的角色如下
************（master03,etcd03）、************（node-03）、************（node-06）
************（node-09）、************（node-ci-01）、************（负载均衡-02）

************ ************ 两台虚拟机在************物理机下，使用了nginx高可用，虚拟ip为************
************ ************ 使用haproxy + keepalived做k8s负载均衡器，虚拟ip为************
************ ************ 使用nfs互相共享，做k8s存储

k8s集群使用kubesphere(v3.1.1)与kubernetes(v1.23.9)
安装文件
apiVersion: kubekey.kubesphere.io/v1alpha1
kind: Cluster
metadata:
  name: kubesphere
spec:
  hosts:
  - { name: kube-master-01, address: ************, internalAddress: ************, user: root, password: bswl@123 }
  - { name: kube-master-02, address: ************, internalAddress: ************, user: root, password: bswl@123 }
  - { name: kube-master-03, address: ************, internalAddress: ************, user: root, password: bswl@123 }
  - { name: kube-node-01, address: ************, internalAddress: ************3, user: root, password: bswl@123 }
  - { name: kube-node-02, address: ************, internalAddress: ************4, user: root, password: bswl@123 }
  - { name: kube-node-03, address: ************, internalAddress: ************5, user: root, password: bswl@123 }
  - { name: kube-node-04, address: ************, internalAddress: ************6, user: root, password: bswl@123 }
  - { name: kube-node-05, address: ************, internalAddress: ************7, user: root, password: bswl@123 }
  - { name: kube-node-06, address: ************, internalAddress: ************8, user: root, password: bswl@123 }
  - { name: kube-node-07, address: ************, internalAddress: ************9, user: root, password: bswl@123 }
  - { name: kube-node-08, address: ************, internalAddress: ************0, user: root, password: bswl@123 }
  - { name: kube-node-09, address: ************, internalAddress: ************1, user: root, password: bswl@123 }
  - { name: kube-node-10, address: ************, internalAddress: ************2, user: root, password: bswl@123 }
  - { name: kube-node-11, address: ************, internalAddress: ************3, user: root, password: bswl@123 }
  - { name: kube-node-ci-01, address: ************, internalAddress: ************4, user: root, password: bswl@123 }
  - { name: kube-node-ci-02, address: ************, internalAddress: ************5, user: root, password: bswl@123 }
  roleGroups:
    etcd:
    - kube-master-01
    - kube-master-02
    - kube-master-03
    master:
    - kube-master-01
    - kube-master-02
    - kube-master-03
    worker:
    - kube-node-01
    - kube-node-02
    - kube-node-03
    - kube-node-04
    - kube-node-05
    - kube-node-06
    - kube-node-07
    - kube-node-08
    - kube-node-09
    - kube-node-10
    - kube-node-11
    - kube-node-ci-01
    - kube-node-ci-02
  controlPlaneEndpoint:
    domain: lb.kubesphere.local
    address: "************"
    port: 6443
  kubernetes:
    version: v1.20.4
    imageRepo: kubesphere
    clusterName: cluster.local
    containerManager: docker
  network:
    plugin: calico
    kubePodsCIDR: ***********/18
    kubeServiceCIDR: **********/18
  registry:
    registryMirrors: []
    insecureRegistries: []
  addons:
  - name: nfs-01
    namespace: kube-system
    sources:
      chart:
        name: nfs-client-provisioner
        repo: https://charts.kubesphere.io/main
        values:
        - storageClass.name=nfs-01
        - storageClass.defaultClass=true
        - nfs.server=************
        - nfs.path=/data
  - name: nfs-02
    namespace: kube-system
    sources:
      chart:
        name: nfs-client-provisioner
        repo: https://charts.kubesphere.io/main
        values:
        - storageClass.name=nfs-02
        - storageClass.defaultClass=false
        - nfs.server=************
        - nfs.path=/data
---
apiVersion: installer.kubesphere.io/v1alpha1
kind: ClusterConfiguration
metadata:
  name: ks-installer
  namespace: kubesphere-system
  labels:
    version: v3.1.1
spec:
  persistence:
    storageClass: ""
  authentication:
    jwtSecret: ""
  zone: ""
  local_registry: ""
  etcd:
    monitoring: true
    endpointIps: localhost
    port: 2379
    tlsEnable: true
  common:
    redis:
      enabled: false
    redisVolumSize: 2Gi
    openldap:
      enabled: false
    openldapVolumeSize: 2Gi
    minioVolumeSize: 20Gi
    monitoring:
      endpoint: http://prometheus-operated.kubesphere-monitoring-system.svc:9090
    es:
      elasticsearchMasterVolumeSize: 8Gi
      elasticsearchDataVolumeSize: 40Gi
      logMaxAge: 90
      elkPrefix: logstash
      basicAuth:
        enabled: false
        username: ""
        password: ""
      externalElasticsearchUrl: ""
      externalElasticsearchPort: ""
  console:
    enableMultiLogin: true
    port: 30880
  alerting:
    enabled: true
    # thanosruler:
    #   replicas: 1
    #   resources: {}
  auditing:
    enabled: false
  devops:
    enabled: true
    jenkinsMemoryLim: 2Gi
    jenkinsMemoryReq: 1500Mi
    jenkinsVolumeSize: 8Gi
    jenkinsJavaOpts_Xms: 512m
    jenkinsJavaOpts_Xmx: 512m
    jenkinsJavaOpts_MaxRAM: 2g
  events:
    enabled: true
    ruler:
      enabled: true
      replicas: 2
  logging:
    enabled: true
    logsidecar:
      enabled: true
      replicas: 2
  metrics_server:
    enabled: true
  monitoring:
    storageClass: ""
    prometheusMemoryRequest: 400Mi
    prometheusVolumeSize: 20Gi
  multicluster:
    clusterRole: none
  network:
    networkpolicy:
      enabled: false
    ippool:
      type: none
    topology:
      type: none
  openpitrix:
    store:
      enabled: true
  servicemesh:
    enabled: true
  kubeedge:
    enabled: false
    cloudCore:
      nodeSelector: { "node-role.kubernetes.io/worker": "" }
      tolerations: []
      cloudhubPort: "10000"
      cloudhubQuicPort: "10001"
      cloudhubHttpsPort: "10002"
      cloudstreamPort: "10003"
      tunnelPort: "10004"
      cloudHub:
        advertiseAddress:
        - ""
        nodeLimit: "100"
      service:
        cloudhubNodePort: "30000"
        cloudhubQuicNodePort: "30001"
        cloudhubHttpsNodePort: "30002"
        cloudstreamNodePort: "30003"
        tunnelNodePort: "30004"
    edgeWatcher:
      nodeSelector: { "node-role.kubernetes.io/worker": "" }
      tolerations: []
      edgeWatcherAgent:
        nodeSelector: { "node-role.kubernetes.io/worker": "" }
        tolerations: []

src/components/Layout.jsx 导航栏第一项添加Kubernetes基础菜单项
子项名为：核心组件