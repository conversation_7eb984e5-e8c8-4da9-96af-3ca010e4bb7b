import React, { useState } from 'react'
import ArchitectureDiagram from '../components/ArchitectureDiagram'
import EnvironmentGuide from '../components/EnvironmentGuide'
import FAQ from '../components/FAQ'
import { 
  ServerIcon,
  CogIcon,
  QuestionMarkCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  UserGroupIcon,
  CubeIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { ServerIcon as ServerSolid } from '@heroicons/react/24/solid'

const LocalClusterPage = () => {
    const [activeTab, setActiveTab] = useState('architecture')
    
    const handleTabChange = (tabId) => {
        setActiveTab(tabId)
        // 滚动到标签内容的开始位置，考虑导航栏高度
        setTimeout(() => {
            const tabContentElement = document.querySelector('.tab-content')
            const tabNavElement = document.querySelector('.tab-navigation')
            if (tabContentElement && tabNavElement) {
                const navHeight = tabNavElement.offsetHeight
                const elementTop = tabContentElement.offsetTop
                const offsetPosition = elementTop - navHeight - 20 // 额外20px间距

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                })
            }
        }, 100) // 短暂延迟确保内容已渲染
    }

    const tabs = [
        { id: 'architecture', name: '架构图', icon: CubeIcon, description: '可视化集群架构' },
        { id: 'environment', name: '环境配置', icon: CogIcon, description: '部署与配置指南' },
        { id: 'faq', name: '问题解答', icon: QuestionMarkCircleIcon, description: '常见问题' },
    ]

    const stats = [
        { 
            label: '物理服务器', 
            value: '3', 
            unit: '台', 
            icon: ServerSolid,
            description: '高性能物理服务器'
        },
        { 
            label: '虚拟机节点', 
            value: '20+', 
            unit: '个', 
            icon: ServerIcon,
            description: 'Kubernetes工作节点'
        },
        { 
            label: '系统可用性', 
            value: '99.9%', 
            unit: '', 
            icon: CheckCircleIcon,
            description: '企业级高可用保障'
        },
        { 
            label: '技术支持', 
            value: '24/7', 
            unit: '', 
            icon: UserGroupIcon,
            description: '全天候技术支持'
        },
    ]

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
            {/* Hero Section */}
            <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                    <div className="text-center">
                        {/* Simple icon */}
                        <div className="mb-8">
                            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20">
                                <ServerSolid className="h-10 w-10 text-white" />
                            </div>
                        </div>
                        
                        <div className="space-y-8">
                            <div>
                                <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">
                                    <span className="block">本地机房</span>
                                    <span className="block bg-gradient-to-r from-blue-200 via-white to-indigo-200 bg-clip-text text-transparent">
                                        K8S集群
                                    </span>
                                </h1>
                                <p className="text-xl md:text-2xl text-blue-100/90 max-w-4xl mx-auto leading-relaxed font-light">
                                    基于物理机虚拟化的Kubernetes集群部署架构与管理文档
                                </p>
                            </div>
                            
                            {/* Status indicators */}
                            <div className="flex flex-wrap justify-center gap-4 pt-8">
                                <div className="inline-flex items-center px-4 py-2 bg-green-500/20 rounded-full backdrop-blur-sm border border-green-400/30">
                                    <div className="w-3 h-3 bg-green-400 rounded-full mr-2" />
                                    <span className="text-green-100 font-medium">生产环境运行</span>
                                </div>
                                <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full backdrop-blur-sm border border-blue-400/30">
                                    <CheckCircleIcon className="w-5 h-5 text-blue-300 mr-2" />
                                    <span className="text-blue-100 font-medium">99.9% 可用性</span>
                                </div>
                                <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full backdrop-blur-sm border border-purple-400/30">
                                    <ServerIcon className="w-5 h-5 text-purple-300 mr-2" />
                                    <span className="text-purple-100 font-medium">20+ 节点</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Stats Section */}
            <div className="relative -mt-20 pb-24">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        {stats.map((stat, index) => {
                            const Icon = stat.icon
                            return (
                                <div 
                                    key={index} 
                                    className="bg-white rounded-3xl p-8 shadow-xl border border-gray-200"
                                >
                                    <div className="text-center">
                                        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg mb-6">
                                            <Icon className="h-8 w-8 text-white" />
                                        </div>
                                        
                                        <div className="space-y-2">
                                            <div className="text-4xl font-bold text-gray-900 tracking-tight">
                                                {stat.value}
                                                {stat.unit && <span className="text-xl text-gray-600 ml-1">{stat.unit}</span>}
                                            </div>
                                            <div className="text-gray-800 font-semibold text-lg">{stat.label}</div>
                                            <div className="text-gray-600 text-sm leading-relaxed">{stat.description}</div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            </div>

            {/* Enhanced Tab Navigation */}
            <div className="tab-navigation sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="flex space-x-1 py-4">
                        {tabs.map((tab) => {
                            const Icon = tab.icon
                            const isActive = activeTab === tab.id
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => handleTabChange(tab.id)}
                                    className={`relative flex items-center px-6 py-4 rounded-xl font-medium text-sm transition-all duration-200 ${
                                        isActive
                                            ? 'bg-blue-600 text-white shadow-lg'
                                            : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                                    }`}
                                >
                                    <Icon className={`mr-3 h-5 w-5 ${
                                        isActive ? 'text-white' : 'text-gray-500'
                                    }`} />
                                    <div className="text-left">
                                        <div className="font-semibold">{tab.name}</div>
                                        <div className={`text-xs mt-0.5 ${
                                            isActive ? 'text-blue-100' : 'text-gray-500'
                                        }`}>
                                            {tab.description}
                                        </div>
                                    </div>
                                </button>
                            )
                        })}
                    </nav>
                </div>
            </div>

            {/* Tab Content */}
            <div className="tab-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {activeTab === 'architecture' && <ArchitectureDiagram />}
                {activeTab === 'environment' && <EnvironmentGuide />}
                {activeTab === 'faq' && <FAQ />}
            </div>
        </div>
    )
}

export default LocalClusterPage
