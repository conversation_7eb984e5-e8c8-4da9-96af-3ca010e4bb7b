import React, { useState } from 'react'
import { ChevronDown, ChevronUp, AlertCircle, CheckCircle, XCircle, Info } from 'lucide-react'

const FAQ = () => {
  const [openItems, setOpenItems] = useState(new Set())

  const toggleItem = (index) => {
    const newOpenItems = new Set(openItems)
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index)
    } else {
      newOpenItems.add(index)
    }
    setOpenItems(newOpenItems)
  }

  const faqData = [
    {
      category: '集群部署问题',
      icon: AlertCircle,
      color: 'text-red-600 bg-red-100',
      questions: [
        {
          question: 'KubeKey安装过程中出现网络超时怎么办？',
          answer: `网络超时通常是由于网络连接不稳定或防火墙限制导致的。解决方案：

1. **检查网络连接**：确保所有节点能够正常访问互联网
2. **配置代理**：如果在企业网络环境中，配置HTTP/HTTPS代理
3. **使用国内镜像源**：
   \`\`\`bash
   export KKZONE=cn
   ./kk create cluster -f config-sample.yaml
   \`\`\`
4. **增加超时时间**：在配置文件中增加超时设置
5. **分步安装**：先安装Kubernetes，再安装KubeSphere`
        },
        {
          question: '节点加入集群失败，提示证书错误？',
          answer: `证书错误通常是时间同步或网络配置问题：

**解决步骤：**
1. **同步时间**：确保所有节点时间同步
   \`\`\`bash
   ntpdate -s time.nist.gov
   \`\`\`
2. **检查hostname**：确保每个节点hostname唯一
3. **清理旧证书**：
   \`\`\`bash
   rm -rf /etc/kubernetes/pki
   kubeadm reset
   \`\`\`
4. **重新生成证书**：使用KubeKey重新初始化集群`
        },
        {
          question: 'etcd集群启动失败怎么处理？',
          answer: `etcd是Kubernetes的核心组件，启动失败需要仔细排查：

**常见原因及解决方案：**
1. **磁盘空间不足**：清理磁盘空间，确保至少有10GB可用空间
2. **端口冲突**：检查2379、2380端口是否被占用
3. **数据目录权限**：确保etcd用户有数据目录的读写权限
4. **网络连通性**：确保etcd节点间网络互通
5. **恢复数据**：如果数据损坏，从备份恢复：
   \`\`\`bash
   etcdctl snapshot restore snapshot.db
   \`\`\``
        }
      ]
    },
    {
      category: '网络配置问题',
      icon: CheckCircle,
      color: 'text-green-600 bg-green-100',
      questions: [
        {
          question: 'Pod无法访问外网怎么解决？',
          answer: `Pod网络问题通常与CNI插件配置相关：

**排查步骤：**
1. **检查CNI插件状态**：
   \`\`\`bash
   kubectl get pods -n kube-system | grep calico
   \`\`\`
2. **检查路由表**：确保节点路由配置正确
3. **检查防火墙**：关闭或正确配置iptables规则
4. **检查DNS**：
   \`\`\`bash
   kubectl get pods -n kube-system | grep coredns
   \`\`\`
5. **重启网络插件**：
   \`\`\`bash
   kubectl delete pods -n kube-system -l k8s-app=calico-node
   \`\`\``
        },
        {
          question: 'Ingress无法正常工作？',
          answer: `Ingress问题通常与网关配置或DNS解析相关：

**检查清单：**
1. **网关状态**：确保项目网关正常运行
2. **DNS解析**：验证域名是否正确解析到负载均衡器IP
3. **Nginx配置**：检查upstream配置是否正确
4. **端口转发**：确认路由器端口转发规则
5. **证书配置**：如果使用HTTPS，检查SSL证书

**测试命令：**
\`\`\`bash
# 测试DNS解析
nslookup p.test.com

# 测试网关连通性
curl -H "Host: p.test.com" http://************
\`\`\``
        },
        {
          question: '负载均衡器VIP漂移异常？',
          answer: `Keepalived VIP漂移问题需要检查高可用配置：

**排查方法：**
1. **检查Keepalived状态**：
   \`\`\`bash
   systemctl status keepalived
   \`\`\`
2. **查看日志**：
   \`\`\`bash
   tail -f /var/log/messages | grep keepalived
   \`\`\`
3. **检查网络接口**：确保VIP绑定到正确的网卡
4. **验证脚本**：检查健康检查脚本是否正常工作
5. **防火墙规则**：确保VRRP协议(112)不被阻止`
        }
      ]
    },
    {
      category: '存储问题',
      icon: XCircle,
      color: 'text-orange-600 bg-orange-100',
      questions: [
        {
          question: 'NFS存储挂载失败？',
          answer: `NFS存储问题通常与权限或网络相关：

**解决步骤：**
1. **检查NFS服务状态**：
   \`\`\`bash
   systemctl status nfs-server
   showmount -e ************
   \`\`\`
2. **验证权限**：确保NFS共享目录权限正确
3. **检查防火墙**：开放NFS相关端口(111, 2049)
4. **测试挂载**：
   \`\`\`bash
   mount -t nfs ************:/data /mnt/test
   \`\`\`
5. **检查StorageClass**：确保nfs-client-provisioner正常运行`
        },
        {
          question: 'PVC一直处于Pending状态？',
          answer: `PVC Pending状态表示无法找到合适的存储：

**排查方法：**
1. **检查StorageClass**：
   \`\`\`bash
   kubectl get storageclass
   \`\`\`
2. **查看PVC详情**：
   \`\`\`bash
   kubectl describe pvc <pvc-name>
   \`\`\`
3. **检查Provisioner**：确保存储供应商Pod正常运行
4. **验证存储容量**：确保NFS服务器有足够空间
5. **检查标签选择器**：确保PVC和PV标签匹配`
        }
      ]
    },
    {
      category: '应用部署问题',
      icon: Info,
      color: 'text-blue-600 bg-blue-100',
      questions: [
        {
          question: 'Pod一直处于ImagePullBackOff状态？',
          answer: `镜像拉取失败通常是网络或认证问题：

**解决方案：**
1. **检查镜像地址**：确保镜像名称和标签正确
2. **网络连通性**：测试节点是否能访问镜像仓库
3. **认证配置**：如果是私有仓库，配置imagePullSecrets
4. **使用本地镜像**：
   \`\`\`bash
   docker save image:tag | ssh node 'docker load'
   \`\`\`
5. **配置镜像加速器**：使用阿里云或其他镜像加速服务`
        },
        {
          question: '应用无法通过域名访问？',
          answer: `域名访问问题需要逐层排查：

**检查流程：**
1. **DNS解析**：确认域名解析到正确IP
2. **端口转发**：验证路由器端口转发规则
3. **Nginx配置**：检查反向代理配置
4. **Ingress规则**：确认应用路由配置正确
5. **Service状态**：检查K8S服务是否正常
6. **Pod状态**：确认后端Pod正常运行

**测试命令：**
\`\`\`bash
# 逐层测试
curl -I http://*************
curl -I http://************
curl -I http://************:31166
\`\`\``
        },
        {
          question: 'KubeSphere界面无法访问？',
          answer: `KubeSphere访问问题检查：

**排查步骤：**
1. **检查Pod状态**：
   \`\`\`bash
   kubectl get pods -n kubesphere-system
   \`\`\`
2. **验证Service**：
   \`\`\`bash
   kubectl get svc -n kubesphere-system
   \`\`\`
3. **检查NodePort**：确认30880端口可访问
4. **查看日志**：
   \`\`\`bash
   kubectl logs -n kubesphere-system deployment/ks-console
   \`\`\`
5. **重启服务**：如果需要，重启相关组件`
        }
      ]
    },
    {
      category: '监控和日志',
      icon: CheckCircle,
      color: 'text-purple-600 bg-purple-100',
      questions: [
        {
          question: 'Prometheus监控数据缺失？',
          answer: `监控数据问题通常与存储或配置相关：

**解决方法：**
1. **检查存储空间**：确保监控数据存储有足够空间
2. **验证配置**：检查Prometheus配置文件
3. **检查采集目标**：
   \`\`\`bash
   kubectl get servicemonitor -A
   \`\`\`
4. **重启组件**：重启Prometheus相关Pod
5. **检查网络**：确保监控组件间网络互通`
        },
        {
          question: '日志收集不完整？',
          answer: `日志收集问题检查：

**排查要点：**
1. **Fluent Bit状态**：检查日志收集器运行状态
2. **存储配置**：验证Elasticsearch存储配置
3. **日志轮转**：检查日志轮转策略
4. **权限问题**：确保日志收集器有读取权限
5. **网络连通性**：验证到日志存储的网络连接`
        }
      ]
    }
  ]

  return (
    <div className="max-w-4xl mx-auto py-2">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">常见问题解答</h2>
        <p className="text-gray-600">
          收集了K8S集群部署和运维过程中的常见问题及解决方案，帮助快速定位和解决问题。
        </p>
      </div>

      <div className="space-y-6">
        {faqData.map((category, categoryIndex) => {
          const CategoryIcon = category.icon
          return (
            <div key={categoryIndex} className="card">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-2 rounded-lg ${category.color}`}>
                  <CategoryIcon className="h-5 w-5" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{category.category}</h3>
              </div>

              <div className="space-y-4">
                {category.questions.map((item, questionIndex) => {
                  const itemIndex = `${categoryIndex}-${questionIndex}`
                  const isOpen = openItems.has(itemIndex)
                  
                  return (
                    <div key={questionIndex} className="border border-gray-200 rounded-lg">
                      <button
                        onClick={() => toggleItem(itemIndex)}
                        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-900 pr-4">
                          {item.question}
                        </span>
                        {isOpen ? (
                          <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                        )}
                      </button>
                      
                      {isOpen && (
                        <div className="px-6 pb-4 border-t border-gray-100">
                          <div className="pt-4 text-gray-700 whitespace-pre-line leading-relaxed">
                            {item.answer}
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default FAQ
