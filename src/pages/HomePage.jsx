import React from 'react'
import {<PERSON>} from 'react-router-dom'
import {
    ServerIcon,
    CloudIcon,
    ShieldCheckIcon,
    CpuChipIcon,
    GlobeAltIcon,
    ArrowRightIcon,
    CheckCircleIcon,
    ChartBarIcon,
    CogIcon
} from '@heroicons/react/24/outline'
import {
    ServerIcon as ServerSolid,
    CloudIcon as CloudSolid
} from '@heroicons/react/24/solid'

const HomePage = () => {
    const features = [
        {
            icon: ServerIcon,
            title: '机房服务器部署',
            description: '基于物理机虚拟化的高可用K8S集群',
        },
        {
            icon: CloudIcon,
            title: '云服务器部署',
            description: '云原生ACK集群解决方案',
        },
        {
            icon: ShieldCheckIcon,
            title: '高可用架构',
            description: '多层负载均衡与故障转移',
        },
        {
            icon: ChartBarIcon,
            title: '监控运维',
            description: '企业级监控与日志管理',
        }
    ]

    const architectureHighlights = [
        {
            icon: GlobeAltIcon,
            title: '公网接入',
            description: '固定IP + 域名解析',
        },
        {
            icon: ShieldCheckIcon,
            title: '负载均衡',
            description: 'Nginx + Keepalived',
        },
        {
            icon: CpuChipIcon,
            title: 'K8S负载均衡',
            description: 'HAProxy + Keepalived',
        },
        {
            icon: CogIcon,
            title: 'NFS存储',
            description: '双节点互备存储',
        }
    ]

    const stats = [
        {label: '物理服务器', value: '3', unit: '台'},
        {label: '虚拟机节点', value: '20+', unit: '个'},
        {label: 'Master节点', value: '3', unit: '个'},
        {label: 'Worker节点', value: '13', unit: '个'}
    ]

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <div className="relative bg-gradient-to-r from-blue-600 to-indigo-700 overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-black opacity-10"></div>
                <div className="absolute inset-0" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                }}></div>

                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
                    <div className="text-center">
                        <div className="mb-8">
                            <div
                                className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-2xl backdrop-blur-sm">
                                <ServerSolid className="h-10 w-10 text-white"/>
                            </div>
                        </div>

                        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
                            Kubernetes集群
                        </h1>

                        <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
                            Kubernetes集群部署与管理解决方案
                            <span className="block mt-2 text-lg text-blue-200">从机房服务器到云服务器的完整容器化平台</span>
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                            <Link
                                to=""
                                className="group inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                <ServerSolid className="mr-3 h-6 w-6"/>
                                机房服务器部署
                            </Link>
                            <Link
                                to=""
                                className="group inline-flex items-center px-8 py-4 bg-white/10 text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-200 backdrop-blur-sm border border-white/20"
                            >
                                <CloudSolid className="mr-3 h-6 w-6"/>
                                云服务器部署
                            </Link>
                        </div>

                        {/* Trust Indicators */}
                        <div className="flex flex-wrap justify-center items-center gap-8 text-blue-200">
                            <div className="flex items-center">
                                <CheckCircleIcon className="h-5 w-5 mr-2"/>
                                <span className="text-sm">生产环境验证</span>
                            </div>
                            <div className="flex items-center">
                                <CheckCircleIcon className="h-5 w-5 mr-2"/>
                                <span className="text-sm">高可用架构</span>
                            </div>
                            <div className="flex items-center">
                                <CheckCircleIcon className="h-5 w-5 mr-2"/>
                                <span className="text-sm">企业级安全</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="py-24 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-20">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            完整的集群解决方案
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            从基础设施规划到应用部署运维，提供端到端的Kubernetes集群管理平台
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {features.map((feature, index) => {
                            const Icon = feature.icon
                            const isClickable = feature.href
                            const Component = isClickable ? Link : 'div'
                            return (
                                <Component
                                    key={index}
                                    className={`group relative bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-blue-200 transition-all duration-300 ${isClickable ? 'cursor-pointer' : ''}`}
                                >
                                    <div
                                        className="flex items-center justify-center w-16 h-16 bg-blue-50 rounded-xl mb-6 group-hover:bg-blue-100 transition-colors">
                                        <Icon className="h-8 w-8 text-blue-600"/>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                                        {feature.title}
                                    </h3>
                                    <p className="text-gray-600 leading-relaxed">
                                        {feature.description}
                                    </p>
                                </Component>
                            )
                        })}
                    </div>
                </div>
            </div>

            {/* Architecture Overview */}
            <div className="bg-gray-50 py-24">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-20">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            架构核心组件
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            高可用、可扩展的企业级架构设计，确保系统稳定性和性能
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {architectureHighlights.map((item, index) => {
                            const Icon = item.icon
                            return (
                                <div key={index} className="">
                                    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                                        <div
                                            className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl mb-6 shadow-lg">
                                            <Icon className="h-8 w-8 text-white"/>
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-900 mb-3">
                                            {item.title}
                                        </h3>
                                        <p className="text-gray-600 mb-4 leading-relaxed">
                                            {item.description}
                                        </p>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            </div>

            {/* Technology Stack */}
            <div className="bg-white py-24">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-20">
                        <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                            技术栈与工具
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            采用业界领先的开源技术，构建稳定可靠的容器化平台
                        </p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                        {[
                            {name: 'Kubernetes', color: 'from-blue-500 to-blue-600'},
                            {name: 'KubeSphere', color: 'from-green-500 to-green-600'},
                            {name: 'Docker', color: 'from-blue-400 to-blue-500'},
                            {name: 'Calico CNI', color: 'from-orange-500 to-orange-600'},
                            {name: 'Nginx LB', color: 'from-green-400 to-green-500'},
                            {name: 'HAProxy K8S LB', color: 'from-red-500 to-red-600'}
                        ].map((tech, index) => (
                            <div key={index} className="group text-center">
                                <div
                                    className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:border-blue-200">
                                    <div
                                        className={`w-16 h-16 bg-gradient-to-br ${tech.color} rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <span className="text-2xl font-bold text-white">
                      {tech.name.charAt(0)}
                    </span>
                                    </div>
                                    <h3 className="font-semibold text-gray-900 mb-2">{tech.name}</h3>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default HomePage
