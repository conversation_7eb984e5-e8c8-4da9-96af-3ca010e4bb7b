import React, {useState} from 'react'
import NodeDetailModal from './NodeDetailModal'
import {
    ServerIcon,
    GlobeAltIcon,
    ShieldCheckIcon,
    BoltIcon,
    CircleStackIcon,
    ComputerDesktopIcon,
    CpuChipIcon,
    CogIcon
} from '@heroicons/react/24/outline'

const ArchitectureDiagram = () => {
    const [selectedNode, setSelectedNode] = useState(null)
    const [modalOpen, setModalOpen] = useState(false)

    const handleNodeClick = (node) => {
        setSelectedNode(node)
        setModalOpen(true)
    }

    const closeModal = () => {
        setModalOpen(false)
        setSelectedNode(null)
    }

    return (
        <div className="w-full space-y-12 max-w-7xl mx-auto py-2">
            {/* Architecture Overview */}
            <div className="bg-white rounded-3xl p-10 shadow-xl border border-gray-200">
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-bold text-gray-900 mb-4">集群架构概览</h2>
                    <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                        企业级高可用Kubernetes集群，完整的容器化解决方案
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
                    <div className="text-center">
                        <div className="relative mb-6">
                            <div className="absolute inset-0 bg-purple-400/20 rounded-full blur-xl"></div>
                            <div
                                className="relative bg-gradient-to-br from-purple-500 to-purple-600 rounded-3xl p-8 w-24 h-24 mx-auto flex items-center justify-center shadow-2xl">
                                <CogIcon className="h-12 w-12 text-white"/>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">3个Master节点</h3>
                        <p className="text-gray-700 leading-relaxed">控制平面高可用<br/>API服务器集群</p>
                    </div>

                    <div className="text-center">
                        <div className="relative mb-6">
                            <div className="absolute inset-0 bg-cyan-400/20 rounded-full blur-xl"></div>
                            <div
                                className="relative bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-3xl p-8 w-24 h-24 mx-auto flex items-center justify-center shadow-2xl">
                                <ComputerDesktopIcon className="h-12 w-12 text-white"/>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">13个Worker节点</h3>
                        <p className="text-gray-700 leading-relaxed">工作负载节点<br/>容器运行环境</p>
                    </div>

                    <div className="text-center">
                        <div className="relative mb-6">
                            <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-xl"></div>
                            <div
                                className="relative bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl p-8 w-24 h-24 mx-auto flex items-center justify-center shadow-2xl">
                                <ShieldCheckIcon className="h-12 w-12 text-white"/>
                            </div>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">高可用架构</h3>
                        <p className="text-gray-700 leading-relaxed">多层负载均衡<br/>自动故障转移</p>
                    </div>
                </div>
            </div>

            {/* Network Flow */}
            <div className="bg-white rounded-3xl p-10 shadow-xl border border-gray-200">
                <div className="text-center mb-10">
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">网络流量路径</h3>
                    <p className="text-gray-700">从公网到Kubernetes服务的完整数据流向</p>
                </div>

                <div className="relative">
                    {/* Flow path for desktop */}
                    <div className="hidden lg:flex items-center justify-between">
                        <div className="flex-1 flex items-center justify-center">
                            <div className="relative group cursor-pointer" onClick={() => handleNodeClick({
                                name: '公网入口',
                                ip: '***************',
                                type: 'internet',
                                description: '运营商提供的固定公网IP地址',
                                components: [
                                    {name: '域名解析', description: 'doc.test.com, api.test.com等子域名解析'},
                                    {name: '端口映射', description: '80/443端口转发到内网'}
                                ]
                            })}>
                                <div
                                    className="bg-gradient-to-br from-red-500 to-red-600 text-white px-8 py-4 rounded-2xl shadow-xl font-semibold text-center min-w-[200px]">
                                    <GlobeAltIcon className="h-6 w-6 mx-auto mb-2"/>
                                    <div className="text-sm">公网入口</div>
                                    <div className="text-xs opacity-90">***************</div>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center px-4">
                            <div className="w-12 h-0.5 bg-gradient-to-r from-red-400 to-orange-400"></div>
                            <div
                                className="w-0 h-0 border-l-[8px] border-l-orange-400 border-y-[6px] border-y-transparent"></div>
                        </div>

                        <div className="flex-1 flex items-center justify-center">
                            <div className="relative group cursor-pointer" onClick={() => handleNodeClick({
                                name: '路由器/交换机',
                                type: 'router',
                                description: '网络设备，负责端口转发和内网连接',
                                components: [
                                    {name: '端口转发', description: '80/443 → ************:80/443'},
                                    {name: '内网路由', description: '***********/24网段管理'}
                                ]
                            })}>
                                <div
                                    className="bg-gradient-to-br from-orange-500 to-orange-600 text-white px-8 py-4 rounded-2xl shadow-xl font-semibold text-center min-w-[200px]">
                                    <CpuChipIcon className="h-6 w-6 mx-auto mb-2"/>
                                    <div className="text-sm">路由器转发</div>
                                    <div className="text-xs opacity-90">端口映射</div>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center px-4">
                            <div className="w-12 h-0.5 bg-gradient-to-r from-orange-400 to-emerald-400"></div>
                            <div
                                className="w-0 h-0 border-l-[8px] border-l-emerald-400 border-y-[6px] border-y-transparent"></div>
                        </div>

                        <div className="flex-1 flex items-center justify-center">
                            <div className="relative group cursor-pointer" onClick={() => handleNodeClick({
                                name: 'Nginx高可用',
                                ip: '************ (VIP)',
                                type: 'loadbalancer',
                                description: 'Nginx + Keepalived 主从高可用负载均衡',
                                components: [
                                    {name: 'Nginx Master', description: '************ - 主节点反向代理'},
                                    {name: 'Nginx Slave', description: '************ - 从节点备份'},
                                    {name: 'Keepalived', description: 'VIP漂移和健康检查'}
                                ]
                            })}>
                                <div
                                    className="bg-gradient-to-br from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-2xl shadow-xl font-semibold text-center min-w-[200px]">
                                    <ShieldCheckIcon className="h-6 w-6 mx-auto mb-2"/>
                                    <div className="text-sm">Nginx高可用</div>
                                    <div className="text-xs opacity-90">************</div>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center px-4">
                            <div className="w-12 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400"></div>
                            <div
                                className="w-0 h-0 border-l-[8px] border-l-blue-400 border-y-[6px] border-y-transparent"></div>
                        </div>

                        <div className="flex-1 flex items-center justify-center">
                            <div
                                className="bg-gradient-to-br from-blue-500 to-blue-600 text-white px-8 py-4 rounded-2xl shadow-xl font-semibold text-center min-w-[200px]">
                                <ServerIcon className="h-6 w-6 mx-auto mb-2"/>
                                <div className="text-sm">K8S服务</div>
                                <div className="text-xs opacity-90">容器应用</div>
                            </div>
                        </div>
                    </div>

                    {/* Flow path for mobile */}
                    <div className="lg:hidden space-y-6">
                        {[
                            {
                                name: '公网入口',
                                ip: '***************',
                                color: 'from-red-500 to-red-600',
                                icon: GlobeAltIcon
                            },
                            {
                                name: '路由器转发',
                                ip: '端口映射',
                                color: 'from-orange-500 to-orange-600',
                                icon: CpuChipIcon
                            },
                            {
                                name: 'Nginx高可用',
                                ip: '************',
                                color: 'from-emerald-500 to-emerald-600',
                                icon: ShieldCheckIcon
                            },
                            {name: 'K8S服务', ip: '容器应用', color: 'from-blue-500 to-blue-600', icon: ServerIcon}
                        ].map((item, index) => {
                            const Icon = item.icon
                            return (
                                <div key={index} className="relative">
                                    <div
                                        className={`bg-gradient-to-br ${item.color} text-white px-6 py-4 rounded-2xl shadow-xl font-semibold text-center`}>
                                        <Icon className="h-6 w-6 mx-auto mb-2"/>
                                        <div className="text-sm">{item.name}</div>
                                        <div className="text-xs opacity-90">{item.ip}</div>
                                    </div>
                                    {index < 3 && (
                                        <div className="flex justify-center my-4">
                                            <div
                                                className="w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[12px] border-t-slate-400"></div>
                                        </div>
                                    )}
                                </div>
                            )
                        })}
                    </div>
                </div>
            </div>

            {/* Main Architecture Diagram */}
            <div className="bg-white rounded-3xl p-10 shadow-xl border border-gray-200 mb-10">
                <div className="text-center mb-12">
                    <h3 className="text-3xl font-bold text-gray-900 mb-4">集群架构详图</h3>
                    <p className="text-gray-700">点击任意节点查看详细信息</p>
                </div>

                <div className="space-y-16">

                    {/* Network Layer */}
                    <div className="text-center space-y-8">
                        <div className="inline-flex items-center space-x-8">
                            <div
                                className="group cursor-pointer"
                                onClick={() => handleNodeClick({
                                    name: '公网入口',
                                    ip: '***************',
                                    type: 'internet',
                                    description: '运营商提供的固定公网IP地址',
                                    components: [
                                        {name: '域名解析', description: 'doc.test.com, api.test.com等子域名解析'},
                                        {name: '端口映射', description: '80/443端口转发到内网'}
                                    ]
                                })}
                            >
                                <div
                                    className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-red-200/50 min-w-[160px]">
                                    <div
                                        className="bg-gradient-to-br from-red-500 to-red-600 rounded-xl p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                                        <GlobeAltIcon className="h-6 w-6 text-white"/>
                                    </div>
                                    <div className="font-semibold text-slate-800">公网入口</div>
                                    <div className="text-xs text-slate-600 mt-1">***************</div>
                                </div>
                            </div>

                            <div className="flex items-center">
                                <div className="w-8 h-0.5 bg-gradient-to-r from-red-400 to-orange-400"></div>
                                <div
                                    className="w-0 h-0 border-l-[6px] border-l-orange-400 border-y-[4px] border-y-transparent"></div>
                            </div>

                            <div
                                className="group cursor-pointer"
                                onClick={() => handleNodeClick({
                                    name: '路由器/交换机',
                                    type: 'router',
                                    description: '网络设备，负责端口转发和内网连接',
                                    components: [
                                        {name: '端口转发', description: '80/443 → ************:80/443'},
                                        {name: '内网路由', description: '***********/24网段管理'}
                                    ]
                                })}
                            >
                                <div
                                    className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-orange-200/50 min-w-[160px]">
                                    <div
                                        className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                                        <CpuChipIcon className="h-6 w-6 text-white"/>
                                    </div>
                                    <div className="font-semibold text-slate-800">路由器</div>
                                    <div className="text-xs text-slate-600 mt-1">端口转发</div>
                                </div>
                            </div>

                            <div className="flex items-center">
                                <div className="w-8 h-0.5 bg-gradient-to-r from-orange-400 to-emerald-400"></div>
                                <div
                                    className="w-0 h-0 border-l-[6px] border-l-emerald-400 border-y-[4px] border-y-transparent"></div>
                            </div>

                            <div
                                className="group cursor-pointer"
                                onClick={() => handleNodeClick({
                                    name: 'Nginx高可用',
                                    ip: '************ (VIP)',
                                    type: 'loadbalancer',
                                    description: 'Nginx + Keepalived 主从高可用负载均衡',
                                    components: [
                                        {name: 'Nginx Master', description: '************ - 主节点反向代理'},
                                        {name: 'Nginx Slave', description: '************ - 从节点备份'},
                                        {name: 'Keepalived', description: 'VIP漂移和健康检查'}
                                    ]
                                })}
                            >
                                <div
                                    className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-emerald-200/50 min-w-[160px]">
                                    <div
                                        className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                                        <ShieldCheckIcon className="h-6 w-6 text-white"/>
                                    </div>
                                    <div className="font-semibold text-slate-800">Nginx高可用</div>
                                    <div className="text-xs text-slate-600 mt-1">************</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Kubernetes Cluster Nodes */}
                    <div className="space-y-12">
                        {/* Master Nodes */}
                        <div>
                            <div className="text-center mb-8">
                                <h4 className="text-2xl font-bold text-gray-900 mb-2">Master节点 (控制平面)</h4>
                                <p className="text-gray-700">Kubernetes集群控制平面，提供API服务和集群管理</p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {[
                                    {
                                        ip: '************',
                                        role: 'master01',
                                        shortRole: 'M1',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-node", desc: "Calico CNI 插件，提供 Pod 网络连接和网络策略（NetworkPolicy）功能，实现跨主机 Pod 通信。" },
                                                { name: "kube-apiserver", desc: "Kubernetes 的核心，提供 REST API 接口，所有操作（kubectl、控制器等）都通过它进行。是整个集群的'前端'。" },
                                                { name: "kube-controller-manager", desc: "运行各种控制器（如节点控制器、副本控制器、服务账户控制器等），负责维护集群的期望状态。" },
                                                { name: "kube-proxy", desc: "负责将 Service 的负载均衡和网络规则（通过 iptables 或 IPVS）实现服务发现和访问。" },
                                                { name: "kube-scheduler", desc: "负责将未调度的 Pod 分配到合适的 Worker 节点上运行。" },
                                                { name: "nodelocaldns", desc: "NodeLocal DNS Cache，提升 DNS 查询性能和可靠性，缓存 CoreDNS 的响应，减少延迟。" },
                                                { name: "etcd", desc: "Kubernetes 的分布式键值存储，用于保存集群所有对象的状态数据（如 Pod、Service、Node 等），是集群的唯一真相来源。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "轻量级日志收集器，负责收集节点上容器和系统日志，并转发到后端（如 Elasticsearch）。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "Prometheus Node Exporter，采集主机级别的指标（CPU、内存、磁盘、网络等），用于监控和告警。" }
                                            ],

                                        }
                                    },
                                    {
                                        ip: '************',
                                        role: 'master02',
                                        shortRole: 'M2',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-node", desc: "Calico CNI 插件，提供 Pod 网络连接和网络策略（NetworkPolicy）功能，实现跨主机 Pod 通信。" },
                                                { name: "kube-apiserver", desc: "Kubernetes 的核心，提供 REST API 接口，所有操作（kubectl、控制器等）都通过它进行。是整个集群的'前端'。" },
                                                { name: "kube-controller-manager", desc: "运行各种控制器（如节点控制器、副本控制器、服务账户控制器等），负责维护集群的期望状态。" },
                                                { name: "kube-proxy", desc: "负责将 Service 的负载均衡和网络规则（通过 iptables 或 IPVS）实现服务发现和访问。" },
                                                { name: "kube-scheduler", desc: "负责将未调度的 Pod 分配到合适的 Worker 节点上运行。" },
                                                { name: "nodelocaldns", desc: "NodeLocal DNS Cache，提升 DNS 查询性能和可靠性，缓存 CoreDNS 的响应，减少延迟。" },
                                                { name: "etcd", desc: "Kubernetes 的分布式键值存储，用于保存集群所有对象的状态数据（如 Pod、Service、Node 等），是集群的唯一真相来源。通常以静态 Pod 形式运行在控制平面节点上。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "轻量级日志收集器，负责收集节点上容器和系统日志，并转发到后端（如 Elasticsearch）。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "Prometheus Node Exporter，采集主机级别的指标（CPU、内存、磁盘、网络等），用于监控和告警。" }
                                            ]
                                        }
                                    },
                                    {
                                        ip: '************',
                                        role: 'master03',
                                        shortRole: 'M3',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-kube-controllers", desc: "Calico 的控制器组件，负责管理网络策略、IP 分配等集群级网络功能，确保 Pod 网络策略生效。" },
                                                { name: "calico-node", desc: "Calico CNI 插件，提供 Pod 网络连接和网络策略（NetworkPolicy）功能，实现跨主机 Pod 通信。" },
                                                { name: "coredns", desc: "Kubernetes 集群的 DNS 服务，为 Service 和 Pod 提供域名解析和服务发现支持。" },
                                                { name: "kube-apiserver", desc: "Kubernetes 的核心，提供 REST API 接口，所有操作（kubectl、控制器等）都通过它进行。是整个集群的“前端”。" },
                                                { name: "kube-controller-manager", desc: "运行各种控制器（如节点控制器、副本控制器、服务账户控制器等），负责维护集群的期望状态。" },
                                                { name: "kube-proxy", desc: "负责将 Service 的负载均衡和网络规则（通过 iptables 或 IPVS）实现服务发现和访问。" },
                                                { name: "kube-scheduler", desc: "负责将未调度的 Pod 分配到合适的 Worker 节点上运行。" },
                                                { name: "nodelocaldns", desc: "NodeLocal DNS Cache，提升 DNS 查询性能和可靠性，缓存 CoreDNS 的响应，减少延迟。" },
                                                { name: "etcd", desc: "Kubernetes 的分布式键值存储，用于保存集群所有对象的状态数据（如 Pod、Service、Node 等），是集群的唯一真相来源。通常以静态 Pod 形式运行在控制平面节点上。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "轻量级日志收集器，负责收集节点上容器和系统日志，并转发到后端（如 Elasticsearch）。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "Prometheus Node Exporter，采集主机级别的指标（CPU、内存、磁盘、网络等），用于监控和告警。" }
                                            ],

                                        }
                                    }
                                ].map((node) => (
                                    <div
                                        key={node.ip}
                                        className="cursor-pointer group"
                                        onClick={() => handleNodeClick({
                                            name: node.role,
                                            ip: node.ip,
                                            type: 'master',
                                            description: 'Kubernetes主节点，运行控制平面组件和KubeSphere管理组件',
                                            namespace: node.namespace
                                        })}
                                    >
                                        <div
                                            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-xl">
                                            <div className="text-center">
                                                <div
                                                    className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                                                    <span
                                                        className="text-white text-xl font-bold">{node.shortRole}</span>
                                                </div>
                                                <h5 className="font-bold text-gray-900 text-lg mb-2">{node.role}</h5>
                                                <p className="text-gray-600 text-sm">{node.ip}</p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Worker Nodes */}
                        <div>
                            <div className="text-center mb-8">
                                <h4 className="text-2xl font-bold text-gray-900 mb-2">Worker节点 (工作负载)</h4>
                                <p className="text-gray-700">运行应用容器的工作节点，提供计算资源</p>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                                {[
                                    {
                                        ip: '************',
                                        role: 'node-01',
                                        shortRole: 'W1',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-node", desc: "Calico CNI 插件 (DaemonSet)，每个节点都运行，提供 Pod 网络连接和网络策略功能。" },
                                                { name: "kube-proxy", desc: "网络代理 (DaemonSet)，每个节点都运行，实现 Service 负载均衡和网络规则。" },
                                                { name: "nodelocaldns", desc: "本地 DNS 缓存 (DaemonSet)，每个节点都运行，提升 DNS 查询性能。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "节点监控 (DaemonSet)，每个节点都运行，采集主机级别的监控指标。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "日志收集器 (DaemonSet)，每个节点都运行，收集容器和系统日志。" }
                                            ]
                                        }
                                    },
                                    {
                                        ip: '************',
                                        role: 'node-02',
                                        shortRole: 'W2',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-node", desc: "Calico CNI 插件 (DaemonSet)，每个节点都运行，提供 Pod 网络连接和网络策略功能。" },
                                                { name: "kube-proxy", desc: "网络代理 (DaemonSet)，每个节点都运行，实现 Service 负载均衡和网络规则。" },
                                                { name: "nodelocaldns", desc: "本地 DNS 缓存 (DaemonSet)，每个节点都运行，提升 DNS 查询性能。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "节点监控 (DaemonSet)，每个节点都运行，采集主机级别的监控指标。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "日志收集器 (DaemonSet)，每个节点都运行，收集容器和系统日志。" }
                                            ]
                                        }
                                    },
                                    {
                                        ip: '************',
                                        role: 'node-03',
                                        shortRole: 'W3',
                                        namespace: {
                                            "kube-system": [
                                                { name: "calico-node", desc: "Calico CNI 插件 (DaemonSet)，每个节点都运行，提供 Pod 网络连接和网络策略功能。" },
                                                { name: "kube-proxy", desc: "网络代理 (DaemonSet)，每个节点都运行，实现 Service 负载均衡和网络规则。" },
                                                { name: "nodelocaldns", desc: "本地 DNS 缓存 (DaemonSet)，每个节点都运行，提升 DNS 查询性能。" }
                                            ],
                                            "kubesphere-monitoring-system": [
                                                { name: "node-exporter", desc: "节点监控 (DaemonSet)，每个节点都运行，采集主机级别的监控指标。" }
                                            ],
                                            "kubesphere-logging-system": [
                                                { name: "fluent-bit", desc: "日志收集器 (DaemonSet)，每个节点都运行，收集容器和系统日志。" }
                                            ]
                                        }
                                    },
                                    {ip: '************', role: 'node-04', shortRole: 'W4'},
                                    {ip: '************', role: 'node-05', shortRole: 'W5'},
                                    {ip: '************', role: 'node-06', shortRole: 'W6'},
                                    {ip: '************', role: 'node-07', shortRole: 'W7'},
                                    {ip: '************', role: 'node-08', shortRole: 'W8'},
                                    {ip: '************', role: 'node-09', shortRole: 'W9'},
                                    {ip: '************', role: 'node-10', shortRole: 'W10'},
                                    {ip: '************', role: 'node-11', shortRole: 'W11'}
                                ].map((node) => (
                                    <div
                                        key={node.ip}
                                        className="cursor-pointer group"
                                        onClick={() => handleNodeClick({
                                            name: node.role,
                                            ip: node.ip,
                                            type: 'worker',
                                            description: 'Kubernetes工作节点，运行应用容器和KubeSphere组件',
                                            namespace: node.namespace || {
                                                "kube-system": [
                                                    { name: "calico-node", desc: "Calico CNI 插件，为 Pod 提供网络连接和网络策略功能。" },
                                                    { name: "kube-proxy", desc: "网络代理，实现 Service 的负载均衡和服务发现。" },
                                                    { name: "nodelocaldns", desc: "本地 DNS 缓存，提升 DNS 查询性能和可靠性。" }
                                                ],
                                                "kubesphere-logging-system": [
                                                    { name: "fluent-bit", desc: "日志收集器，收集节点上的容器和系统日志。" }
                                                ],
                                                "kubesphere-monitoring-system": [
                                                    { name: "node-exporter", desc: "节点监控指标采集器，采集 CPU、内存、磁盘等指标。" }
                                                ]
                                            }
                                        })}
                                    >
                                        <div
                                            className="bg-white rounded-xl p-4 shadow-md border border-gray-200 hover:shadow-lg">
                                            <div className="text-center">
                                                <div
                                                    className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                                                    <span
                                                        className="text-white text-sm font-bold">{node.shortRole}</span>
                                                </div>
                                                <h6 className="font-semibold text-gray-900 text-sm mb-1">{node.role}</h6>
                                                <p className="text-gray-600 text-xs">{node.ip}</p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Support Services */}
                        <div>
                            <div className="text-center mb-8">
                                <h4 className="text-2xl font-bold text-gray-900 mb-2">支持服务</h4>
                                <p className="text-gray-700">负载均衡、存储和CI/CD等支持服务</p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                {[
                                    {ip: '************', role: '负载均衡-01', type: 'lb', shortRole: 'LB1'},
                                    {ip: '************', role: '负载均衡-02', type: 'lb', shortRole: 'LB2'},
                                    {ip: '************', role: 'nfs存储-01', type: 'storage', shortRole: 'NFS1'},
                                    {ip: '************', role: 'nfs存储-02', type: 'storage', shortRole: 'NFS2'},
                                    {ip: '************', role: 'node-ci-01', type: 'ci', shortRole: 'CI1'},
                                    {ip: '************', role: 'node-ci-02', type: 'ci', shortRole: 'CI2'}
                                ].map((node) => {
                                    const colors = {
                                        lb: 'from-indigo-500 to-indigo-600',
                                        storage: 'from-gray-500 to-gray-600',
                                        ci: 'from-yellow-500 to-yellow-600'
                                    }

                                    return (
                                        <div
                                            key={node.ip}
                                            className="cursor-pointer group"
                                            onClick={() => handleNodeClick({
                                                name: node.role,
                                                ip: node.ip,
                                                type: node.type,
                                                description: `${node.type === 'lb' ? 'K8S负载均衡节点' :
                                                    node.type === 'storage' ? '存储节点' : 'CI/CD节点'}`,
                                                components: node.type === 'lb' ? [
                                                    {name: 'HAProxy', description: 'K8S API负载均衡'},
                                                    {name: 'Keepalived', description: 'VIP管理'}
                                                ] : node.type === 'storage' ? [
                                                    {name: 'NFS Server', description: 'NFS存储服务'},
                                                    {name: 'Storage Pool', description: '/data存储池'}
                                                ] : [
                                                    {name: 'Jenkins', description: 'CI/CD工具'},
                                                    {name: 'Docker', description: '容器运行时'}
                                                ]
                                            })}
                                        >
                                            <div
                                                className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-xl">
                                                <div className="text-center">
                                                    <div
                                                        className={`w-14 h-14 bg-gradient-to-br ${colors[node.type]} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                                                        <span
                                                            className="text-white text-sm font-bold">{node.shortRole}</span>
                                                    </div>
                                                    <h5 className="font-bold text-gray-900 text-base mb-2">{node.role}</h5>
                                                    <p className="text-gray-600 text-sm">{node.ip}</p>
                                                </div>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    </div>

                    {/* K8S Load Balancer */}
                    <div className="text-center mt-16">
                        <div className="inline-block">
                            <div
                                className="cursor-pointer group"
                                onClick={() => handleNodeClick({
                                    name: 'K8S负载均衡',
                                    ip: '************ (VIP)',
                                    type: 'k8s-lb',
                                    description: 'HAProxy + Keepalived K8S API服务器负载均衡',
                                    components: [
                                        {name: 'HAProxy Master', description: '************ - 主节点负载均衡'},
                                        {name: 'HAProxy Slave', description: '************ - 从节点备份'},
                                        {name: 'Keepalived', description: 'VIP漂移和健康检查'},
                                        {name: 'API负载均衡', description: '分发请求到3个Master节点'}
                                    ]
                                })}
                            >
                                <div
                                    className="bg-white rounded-2xl p-6 shadow-xl border border-gray-200 min-w-[200px]">
                                    <div
                                        className="bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl p-3 w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                                        <BoltIcon className="h-6 w-6 text-white"/>
                                    </div>
                                    <div className="font-semibold text-gray-900">K8S负载均衡</div>
                                    <div className="text-xs text-gray-600 mt-1">************ (VIP)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Node Detail Modal */}
            <NodeDetailModal
                isOpen={modalOpen}
                onClose={closeModal}
                node={selectedNode}
            />
        </div>
    )
}

export default ArchitectureDiagram
