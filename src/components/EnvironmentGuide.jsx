import React, { useState } from 'react'
import { 
  Globe, 
  Network, 
  Server, 
  Settings, 
  Code,
  Copy,
  CheckCircle,
  ExternalLink
} from 'lucide-react'

const EnvironmentGuide = () => {
  const [copiedText, setCopiedText] = useState('')

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text)
    setCopiedText(label)
    setTimeout(() => setCopiedText(''), 2000)
  }

  const CodeBlock = ({ title, code, language = 'bash' }) => (
    <div className="bg-gray-900 rounded-lg overflow-hidden ">
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800">
        <span className="text-sm font-medium text-gray-300">{title}</span>
        <button
          onClick={() => copyToClipboard(code, title)}
          className="flex items-center space-x-1 text-gray-400 hover:text-white transition-colors"
        >
          {copiedText === title ? (
            <CheckCircle className="h-4 w-4 text-green-400" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
          <span className="text-xs">
            {copiedText === title ? '已复制' : '复制'}
          </span>
        </button>
      </div>
      <pre className="p-4 text-sm text-gray-300 overflow-x-auto">
        <code>{code}</code>
      </pre>
    </div>
  )

  return (
    <div className="max-w-6xl mx-auto space-y-8 py-2">
      
      {/* Overview */}
      <div className="card">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">环境使用指南</h2>
        <p className="text-gray-600 mb-6">
          本指南详细介绍了如何配置和使用本地机房K8S集群环境，包括域名解析、负载均衡配置、服务部署等。
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Globe className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold text-blue-900">域名配置</h3>
            <p className="text-sm text-blue-700">DNS解析与域名管理</p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <Network className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-semibold text-green-900">负载均衡</h3>
            <p className="text-sm text-green-700">Nginx反向代理配置</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <Server className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-semibold text-purple-900">K8S部署</h3>
            <p className="text-sm text-purple-700">应用路由与服务配置</p>
          </div>
        </div>
      </div>

      {/* Domain Configuration */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Globe className="h-6 w-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">1. 域名配置</h3>
        </div>
        
        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">基础信息</h4>
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-blue-900">公网IP:</span>
                  <span className="text-blue-700 ml-2 font-mono">***************</span>
                </div>
                <div>
                  <span className="font-medium text-blue-900">主域名:</span>
                  <span className="text-blue-700 ml-2">test.com</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">DNS解析配置</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">子域名</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">记录类型</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">解析值</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">用途</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {[
                    { subdomain: 'doc.test.com', type: 'A', value: '***************', purpose: '文档系统' },
                    { subdomain: 'api.test.com', type: 'A', value: '***************', purpose: 'API服务' },
                    { subdomain: 'k8sweb.test.com', type: 'A', value: '***************', purpose: 'KubeSphere管理界面' },
                    { subdomain: 'p.test.com', type: 'A', value: '***************', purpose: '项目平台' },
                    { subdomain: 'c.test.com', type: 'A', value: '***************', purpose: '客户端应用' },
                    { subdomain: 'openapi.test.com', type: 'A', value: '***************', purpose: '开放API' }
                  ].map((record, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3 text-sm font-mono text-gray-900">{record.subdomain}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">{record.type}</td>
                      <td className="px-4 py-3 text-sm font-mono text-blue-600">{record.value}</td>
                      <td className="px-4 py-3 text-sm text-gray-600">{record.purpose}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Load Balancer Configuration */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Network className="h-6 w-6 text-green-600" />
          <h3 className="text-xl font-semibold text-gray-900">2. 负载均衡配置</h3>
        </div>

        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">路由器端口转发</h4>
            <div className="bg-orange-50 rounded-lg p-4">
              <p className="text-orange-800 text-sm mb-2">
                将公网IP的80、443端口转发到内网负载均衡器
              </p>
              <div className="font-mono text-sm text-orange-900">
                ***************:80 → ************:80<br/>
                ***************:443 → ************:443
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">Nginx高可用配置</h4>
            <div className="space-y-4">
              <div>
                <h5 className="font-medium text-gray-800 mb-2">KubeSphere管理界面配置</h5>
                <CodeBlock
                  title="k8sweb.test.com - KubeSphere配置"
                  code={`upstream k8s-web {
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
    server ************:30880 weight=1 max_fails=2 fail_timeout=30;
}

server {
    listen 80;
    server_name k8sweb.test.com;
    charset utf-8;
    
    location / {
        proxy_pass              http://k8s-web;
        proxy_cookie_path       /                   /;
        proxy_set_header        Host                $host;
        proxy_set_header        X-Real-IP           $remote_addr;
        proxy_set_header        X-Forwarded-For     $proxy_add_x_forwarded_for;
        proxy_set_header        X-Forwarded-Proto   $scheme;
    }
}`}
                />
              </div>

              <div>
                <h5 className="font-medium text-gray-800 mb-2">项目平台配置</h5>
                <CodeBlock
                  title="项目平台 - 多域名配置"
                  code={`upstream project-platform {
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
    server ************:31166 weight=1 max_fails=2 fail_timeout=30;
}

server {
    listen 80;
    server_name p.test.com c.test.com api.test.com;
    server_name openapi.test.com doc.open.test.com;
    charset utf-8;
    
    location / {
        proxy_pass              http://project-platform;
        proxy_cookie_path       /                   /;
        proxy_set_header        Host                $host;
        proxy_set_header        X-Real-IP           $remote_addr;
        proxy_set_header        X-Forwarded-For     $proxy_add_x_forwarded_for;
        proxy_set_header        X-Forwarded-Proto   $scheme;
    }
}`}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Kubernetes Configuration */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Server className="h-6 w-6 text-purple-600" />
          <h3 className="text-xl font-semibold text-gray-900">3. Kubernetes服务部署</h3>
        </div>

        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">项目网关配置</h4>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-purple-900">访问方式:</span>
                  <span className="text-purple-700 ml-2">NodePort</span>
                </div>
                <div>
                  <span className="font-medium text-purple-900">网关地址:</span>
                  <span className="text-purple-700 ml-2">************</span>
                </div>
                <div>
                  <span className="font-medium text-purple-900">HTTP端口:</span>
                  <span className="text-purple-700 ml-2">31166</span>
                </div>
                <div>
                  <span className="font-medium text-purple-900">HTTPS端口:</span>
                  <span className="text-purple-700 ml-2">31482</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">应用路由(Ingress)配置示例</h4>
            <CodeBlock
              title="pos-web应用路由配置"
              code={`kind: Ingress
apiVersion: extensions/v1beta1
metadata:
  name: pos-web
  namespace: project-platform
  labels:
    app: pos-web
    app.kubernetes.io/instance: pos-web
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: pos-web
    app.kubernetes.io/version: 1.0.0
    helm.sh/chart: pos-web-1.0.0
    version: 1.0.0
  annotations:
    meta.helm.sh/release-name: pos-web
    meta.helm.sh/release-namespace: project-platform
spec:
  rules:
    - host: p.test.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              serviceName: pos-web
              servicePort: 80`}
              language="yaml"
            />
          </div>

          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">服务部署流程</h4>
            <div className="space-y-3">
              {[
                { step: 1, title: '创建应用', desc: '在KubeSphere中创建应用和服务' },
                { step: 2, title: '配置Ingress', desc: '设置应用路由，绑定域名到服务' },
                { step: 3, title: '更新Nginx', desc: '在负载均衡器中添加对应的upstream配置' },
                { step: 4, title: '测试访问', desc: '通过域名访问验证服务是否正常' }
              ].map((item) => (
                <div key={item.step} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {item.step}
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900">{item.title}</h5>
                    <p className="text-sm text-gray-600">{item.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Storage Configuration */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <Settings className="h-6 w-6 text-gray-600" />
          <h3 className="text-xl font-semibold text-gray-900">4. 存储配置</h3>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">NFS存储配置</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">NFS-01 (主存储)</h5>
                <div className="text-sm space-y-1">
                  <div><span className="font-medium">服务器:</span> 192.168.1.71</div>
                  <div><span className="font-medium">路径:</span> /data</div>
                  <div><span className="font-medium">存储类:</span> nfs-01 (默认)</div>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">NFS-02 (备份存储)</h5>
                <div className="text-sm space-y-1">
                  <div><span className="font-medium">服务器:</span> 192.168.1.72</div>
                  <div><span className="font-medium">路径:</span> /data</div>
                  <div><span className="font-medium">存储类:</span> nfs-02</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Access URLs */}
      <div className="card">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">5. 常用访问地址</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            { name: 'KubeSphere管理界面', url: 'http://k8sweb.test.com', desc: 'K8S集群管理平台' },
            { name: '项目平台', url: 'http://p.test.com', desc: '主要业务应用' },
            { name: 'API服务', url: 'http://api.test.com', desc: '后端API接口' },
            { name: '文档系统', url: 'http://doc.test.com', desc: '项目文档' }
          ].map((item, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{item.name}</h4>
                  <p className="text-sm text-gray-600 mb-2">{item.desc}</p>
                  <a 
                    href={item.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm font-mono flex items-center"
                  >
                    {item.url}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default EnvironmentGuide
