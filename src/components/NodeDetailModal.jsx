import React from 'react'
import { Dialog } from '@headlessui/react'
import {
  XMarkIcon,
  ServerIcon,
  GlobeAltIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  BoltIcon,
  CircleStackIcon,
  ComputerDesktopIcon,
  CogIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

const NodeDetailModal = ({ isOpen, onClose, node }) => {
  if (!node) return null

  const getNodeIcon = (type) => {
    switch (type) {
      case 'internet': return GlobeAltIcon
      case 'router': return CpuChipIcon
      case 'loadbalancer': return ShieldCheckIcon
      case 'physical': return ServerIcon
      case 'master': return CogIcon
      case 'worker': return ComputerDesktopIcon
      case 'storage': return CircleStackIcon
      case 'ci': return ChartBarIcon
      case 'lb': return BoltIcon
      case 'k8s-lb': return BoltIcon
      default: return ServerIcon
    }
  }

  const getNodeColor = (type) => {
    switch (type) {
      case 'internet': return 'text-red-600 bg-red-100'
      case 'router': return 'text-orange-600 bg-orange-100'
      case 'loadbalancer': return 'text-green-600 bg-green-100'
      case 'physical': return 'text-blue-600 bg-blue-100'
      case 'master': return 'text-purple-600 bg-purple-100'
      case 'worker': return 'text-cyan-600 bg-cyan-100'
      case 'storage': return 'text-gray-600 bg-gray-100'
      case 'ci': return 'text-yellow-600 bg-yellow-100'
      case 'lb': return 'text-indigo-600 bg-indigo-100'
      case 'k8s-lb': return 'text-indigo-600 bg-indigo-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'internet': return '公网入口'
      case 'router': return '网络设备'
      case 'loadbalancer': return '负载均衡'
      case 'physical': return '物理服务器'
      case 'master': return 'Kubernetes主节点'
      case 'worker': return 'Kubernetes工作节点'
      case 'storage': return '存储节点'
      case 'ci': return 'CI/CD节点'
      case 'lb': return 'K8S负载均衡'
      case 'k8s-lb': return 'K8S负载均衡'
      default: return '未知类型'
    }
  }

  const Icon = getNodeIcon(node.type)
  const colorClass = getNodeColor(node.type)

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full max-h-[85vh] sm:max-h-[80vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center space-x-4">
              <div className={`p-3 rounded-lg ${colorClass}`}>
                <Icon className="h-8 w-8" />
              </div>
              <div>
                <Dialog.Title className="text-xl font-semibold text-gray-900">
                  {node.name}
                </Dialog.Title>
                <p className="text-sm text-gray-600">{getTypeLabel(node.type)}</p>
                {node.ip && (
                  <p className="text-sm font-mono text-blue-600">{node.ip}</p>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-lg hover:bg-gray-100"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto flex-1 scrollbar-thin">
            {/* Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">节点描述</h3>
              <p className="text-gray-700 leading-relaxed">{node.description}</p>
            </div>

            {/* Namespace Components */}
            {node.namespace && Object.keys(node.namespace).length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">运行组件 (按命名空间分组)</h3>
                <div className="space-y-6">
                  {Object.entries(node.namespace).map(([namespaceName, components]) => (
                    <div key={namespaceName} className="border border-gray-200 rounded-lg overflow-hidden">
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3">
                        <h4 className="font-semibold text-white flex items-center">
                          <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                          {namespaceName}
                          <span className="ml-2 text-xs bg-white/20 px-2 py-1 rounded-full">
                            {components.length} 个组件
                          </span>
                        </h4>
                      </div>
                      <div className="p-4 space-y-3">
                        {components.map((component, index) => (
                          <div key={index} className="border-l-4 border-blue-200 pl-4 py-2 hover:bg-gray-50 rounded-r-lg">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                              <div className="flex-1">
                                <h5 className="font-semibold text-gray-900 mb-1">{component.name}</h5>
                                <p className="text-gray-600 text-sm leading-relaxed">{component.desc}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Legacy Components (for backward compatibility) */}
            {node.components && node.components.length > 0 && !node.namespace && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">组件详情</h3>
                <div className="space-y-3">
                  {node.components.map((component, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 mb-1">
                            {component.name}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {component.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Additional Info based on node type */}
            {node.type === 'master' && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">集群信息</h3>
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-900">Kubernetes版本:</span>
                      <span className="text-blue-700 ml-2">v1.23.9</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-900">KubeSphere版本:</span>
                      <span className="text-blue-700 ml-2">v3.1.1</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-900">网络插件:</span>
                      <span className="text-blue-700 ml-2">Calico</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-900">容器运行时:</span>
                      <span className="text-blue-700 ml-2">Docker</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {node.type === 'storage' && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">存储信息</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div>
                      <span className="font-medium text-gray-900">存储路径:</span>
                      <span className="text-gray-700 ml-2 font-mono">/data</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">存储类型:</span>
                      <span className="text-gray-700 ml-2">NFS网络存储</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">高可用:</span>
                      <span className="text-gray-700 ml-2">双节点互相备份</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {(node.type === 'loadbalancer' || node.type === 'k8s-lb') && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">负载均衡配置</h3>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium text-green-900">高可用模式:</span>
                      <span className="text-green-700 ml-2">主从热备</span>
                    </div>
                    <div>
                      <span className="font-medium text-green-900">健康检查:</span>
                      <span className="text-green-700 ml-2">自动故障转移</span>
                    </div>
                    <div>
                      <span className="font-medium text-green-900">VIP漂移:</span>
                      <span className="text-green-700 ml-2">Keepalived管理</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              关闭
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  )
}

export default NodeDetailModal
